#!/usr/bin/env python3
"""
Final comprehensive test of the enhanced Etsy scraper
Tests the complete workflow with the user's specific URL
"""

import sys
import time
from etsy import scrape_etsy_products_with_reviews, setup_etsy_chrome_driver, extract_review_data_smart, extract_product_info_from_html

def test_complete_workflow():
    """Test the complete scraping workflow"""
    
    print("🧪 Final Comprehensive Test")
    print("=" * 50)
    
    # Test with a small sample
    search_query = "jewelry"
    max_products = 2
    min_delay = 8
    max_delay = 12
    csv_filename = "final_test_results.csv"
    
    print(f"📋 Test Parameters:")
    print(f"   Search Query: {search_query}")
    print(f"   Max Products: {max_products}")
    print(f"   Delay: {min_delay}-{max_delay} seconds")
    print(f"   Output: {csv_filename}")
    
    try:
        # Run the complete scraper
        scrape_etsy_products_with_reviews(
            search_query=search_query,
            max_products=max_products,
            min_delay=min_delay,
            max_delay=max_delay,
            csv_filename=csv_filename
        )
        
        # Check if CSV was created and has content
        import os
        if os.path.exists(csv_filename):
            print(f"\n✅ CSV file created: {csv_filename}")
            
            # Read and display results
            import csv
            with open(csv_filename, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                rows = list(reader)
                
            print(f"📊 Results Summary:")
            print(f"   Total products: {len(rows)}")
            
            for i, row in enumerate(rows, 1):
                print(f"\n   Product {i}:")
                print(f"     Title: {row['title'][:50]}...")
                print(f"     Price: {row['price']}")
                print(f"     Reviews: {row['total_reviews']}")
                print(f"     Most Recent: {row['most_recent_review_date']}")
                print(f"     Image URL: {'✅ Found' if row['image_url'] != 'N/A' else '❌ Missing'}")
        else:
            print(f"❌ CSV file not created: {csv_filename}")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

def test_specific_url_only():
    """Test just the specific URL provided by user"""
    
    print("🎯 Testing Specific URL Only")
    print("=" * 40)
    
    test_url = "https://www.etsy.com/listing/4344728865/tiffany-purchase-rings-womens-rings?click_key=71b999f4-25bb-43a8-88c9-a4f2fb9ace9f%3A3b0cfd6e27982486c93992c62e80198ec362d14d&click_sum=fbc063d0&ls=s&ga_order=most_relevant&ga_search_type=vintage&ga_view_type=gallery&ga_search_query=jewelry&ref=search_grid-708127-1-1&sr_prefetch=0&pro=1&pop=1&sts=1&content_source=71b999f4-25bb-43a8-88c9-a4f2fb9ace9f%253A3b0cfd6e27982486c93992c62e80198ec362d14d"
    
    driver = setup_etsy_chrome_driver()
    
    try:
        print(f"🌐 Loading URL...")
        driver.get(test_url)
        time.sleep(8)
        
        # Extract product info
        print("🔍 Extracting product info...")
        product_info = extract_product_info_from_html(driver.page_source, test_url, "jewelry")
        
        # Extract review data
        print("🔍 Extracting review data...")
        review_data = extract_review_data_smart(driver)
        
        # Combine and display results
        if product_info and review_data:
            combined_data = {**product_info, **review_data}
            
            print("\n📊 FINAL RESULTS:")
            print("=" * 30)
            for key, value in combined_data.items():
                print(f"{key}: {value}")
            
            # Save to CSV
            import csv
            fieldnames = [
                'search_query', 'title', 'price', 'product_url', 'image_url',
                'total_reviews', 'most_recent_review_date', 'oldest_review_date'
            ]
            
            with open('specific_url_test.csv', 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerow(combined_data)
            
            print(f"\n✅ Results saved to: specific_url_test.csv")
            
            # Validation
            print(f"\n✅ VALIDATION:")
            print(f"   Product extracted: {'✅' if product_info['title'] != 'N/A' else '❌'}")
            print(f"   Price extracted: {'✅' if product_info['price'] != 'N/A' else '❌'}")
            print(f"   Image extracted: {'✅' if product_info['image_url'] != 'N/A' else '❌'}")
            print(f"   Reviews found: {'✅' if review_data['total_reviews'] > 0 else '❌'}")
            print(f"   Recent date: {'✅' if review_data['most_recent_review_date'] else '❌'}")
            
        else:
            print("❌ Failed to extract data")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        driver.quit()

if __name__ == "__main__":
    print("Final Test Suite for Enhanced Etsy Scraper")
    print("=" * 50)
    print("1. Test complete workflow (2 products)")
    print("2. Test specific URL only")
    
    choice = input("\nChoose test (1 or 2): ").strip()
    
    if choice == "1":
        test_complete_workflow()
    elif choice == "2":
        test_specific_url_only()
    else:
        print("Invalid choice")
