#!/usr/bin/env python3
"""
Ultra Stealth Test for Maximum Bot Detection Avoidance
Tests the enhanced stealth measures against Etsy's bot detection
"""

import sys
import time
import random
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# Import our enhanced functions
from etsy import setup_etsy_chrome_driver, add_human_behavior, random_delay

def test_bot_detection():
    """Test if our stealth measures can bypass bot detection"""
    
    print("🕵️ ULTRA STEALTH BOT DETECTION TEST")
    print("=" * 50)
    
    driver = None
    
    try:
        print("🚀 Setting up MAXIMUM STEALTH driver...")
        driver = setup_etsy_chrome_driver()
        
        # Test URLs that are known to have strong bot detection
        test_urls = [
            "https://www.etsy.com/",
            "https://www.etsy.com/search?q=jewelry",
            "https://www.etsy.com/listing/4344728865/tiffany-purchase-rings-womens-rings"
        ]
        
        for i, url in enumerate(test_urls, 1):
            print(f"\n🎯 Test {i}/3: {url}")
            
            # Add human behavior before navigation
            if i > 1:
                add_human_behavior(driver)
                random_delay(3, 6)
            
            print(f"🌐 Navigating to: {url}")
            driver.get(url)
            
            # Wait for page load
            time.sleep(5)
            
            # Add human behavior after load
            add_human_behavior(driver)
            
            # Check for bot detection
            page_source = driver.page_source.lower()
            page_title = driver.title.lower()
            
            # Bot detection indicators
            bot_indicators = [
                "access blocked",
                "unusual activity",
                "automated",
                "bot activity",
                "captcha",
                "verify you are human",
                "security check",
                "suspicious activity"
            ]
            
            detected = False
            for indicator in bot_indicators:
                if indicator in page_source or indicator in page_title:
                    print(f"❌ BOT DETECTED: Found '{indicator}'")
                    detected = True
                    break
            
            if not detected:
                print("✅ STEALTH SUCCESS: No bot detection found")
                
                # Additional checks
                if "etsy" in page_title:
                    print("✅ Page loaded correctly")
                else:
                    print("⚠️ Unexpected page title")
                
                # Check if we can find normal page elements
                try:
                    # Look for normal Etsy elements
                    normal_elements = driver.find_elements(By.CSS_SELECTOR, "a, button, input")
                    if len(normal_elements) > 10:
                        print(f"✅ Found {len(normal_elements)} interactive elements")
                    else:
                        print("⚠️ Few interactive elements found")
                except:
                    print("⚠️ Could not check page elements")
            else:
                print("❌ STEALTH FAILED: Bot detection triggered")
                
                # Save screenshot for debugging
                try:
                    screenshot_path = f"bot_detection_test_{i}.png"
                    driver.save_screenshot(screenshot_path)
                    print(f"📸 Screenshot saved: {screenshot_path}")
                except:
                    pass
            
            # Human-like delay between tests
            if i < len(test_urls):
                random_delay(5, 10)
        
        # Final stealth verification
        print(f"\n🔍 FINAL STEALTH VERIFICATION")
        print("-" * 30)
        
        # Check navigator properties
        navigator_checks = [
            "navigator.webdriver",
            "navigator.plugins.length",
            "navigator.languages",
            "window.chrome"
        ]
        
        for check in navigator_checks:
            try:
                result = driver.execute_script(f"return {check};")
                print(f"   {check}: {result}")
            except Exception as e:
                print(f"   {check}: Error - {e}")
        
        print(f"\n🎉 Ultra Stealth Test Complete!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        if driver:
            print("\n⏸️ Keeping browser open for 15 seconds for manual inspection...")
            time.sleep(15)
            print("Closing browser...")
            driver.quit()

def test_human_behavior():
    """Test human behavior simulation"""
    
    print("🤖 HUMAN BEHAVIOR SIMULATION TEST")
    print("=" * 40)
    
    driver = setup_etsy_chrome_driver()
    
    try:
        driver.get("https://www.etsy.com/")
        time.sleep(3)
        
        print("🎭 Simulating human behavior...")
        
        for i in range(5):
            print(f"   Behavior simulation {i+1}/5")
            add_human_behavior(driver)
            random_delay(2, 4)
        
        print("✅ Human behavior simulation complete")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        
    finally:
        driver.quit()

def test_specific_etsy_url_stealth():
    """Test the specific URL with maximum stealth"""
    
    print("🎯 SPECIFIC URL STEALTH TEST")
    print("=" * 35)
    
    test_url = "https://www.etsy.com/listing/4344728865/tiffany-purchase-rings-womens-rings"
    
    driver = setup_etsy_chrome_driver()
    
    try:
        print("🕵️ Approaching target with maximum stealth...")
        
        # First visit Etsy homepage to establish session
        print("1️⃣ Establishing session on homepage...")
        driver.get("https://www.etsy.com/")
        add_human_behavior(driver)
        random_delay(3, 6)
        
        # Navigate to search page
        print("2️⃣ Navigating to search...")
        driver.get("https://www.etsy.com/search?q=jewelry")
        add_human_behavior(driver)
        random_delay(4, 7)
        
        # Finally navigate to target URL
        print("3️⃣ Accessing target product...")
        driver.get(test_url)
        add_human_behavior(driver)
        time.sleep(5)
        
        # Check for success
        page_source = driver.page_source.lower()
        
        if "access blocked" in page_source:
            print("❌ BLOCKED: Still getting access blocked")
        elif "tiffany" in page_source:
            print("✅ SUCCESS: Product page loaded successfully")
            
            # Look for reviews
            if "review" in page_source:
                print("✅ Reviews section found")
            else:
                print("⚠️ No reviews found")
        else:
            print("⚠️ Unexpected page content")
        
        print(f"📄 Page title: {driver.title}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        
    finally:
        print("\n⏸️ Keeping browser open for inspection...")
        input("Press Enter to close browser...")
        driver.quit()

if __name__ == "__main__":
    print("Ultra Stealth Test Suite")
    print("=" * 30)
    print("1. Bot detection test")
    print("2. Human behavior test") 
    print("3. Specific URL stealth test")
    
    choice = input("\nChoose test (1, 2, or 3): ").strip()
    
    if choice == "1":
        test_bot_detection()
    elif choice == "2":
        test_human_behavior()
    elif choice == "3":
        test_specific_etsy_url_stealth()
    else:
        print("Invalid choice")
