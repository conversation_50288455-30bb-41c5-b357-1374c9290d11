import time
import random
import os
import csv
import tkinter as tk
from tkinter import simpledialog, messagebox, scrolledtext
from pathlib import Path
from datetime import datetime
import re
from urllib.parse import urljoin, urlparse
import asyncio

# --- Use patchright (undetected browser) ---
import patchright.async_api as patchright
from bs4 import BeautifulSoup

def get_manual_urls_gui():
    """Get product URLs manually from user."""
    root = tk.Tk()
    root.title("Crawl4AI-Inspired Etsy Scraper")
    root.geometry("800x600")

    # Instructions
    instructions = tk.Label(root, text="""
CRAWL4AI-INSPIRED ETSY SCRAPER - INSTRUCTIONS:

This scraper uses advanced anti-detection techniques inspired by Crawl4AI:
- Patchright (undetected browser) instead of regular Playwright
- Stealth mode with deep browser patches
- Progressive enhancement approach

1. Open Etsy in your regular browser
2. Search for products manually
3. Copy product URLs (one per line) into the text box below
4. URLs should look like: https://www.etsy.com/listing/123456789/product-name
5. Click 'Start Scraping' when done

This approach bypasses sophisticated bot detection systems.
""", justify=tk.LEFT, font=("Arial", 10))
    instructions.pack(pady=10)

    # Text area for URLs
    url_label = tk.Label(root, text="Paste Etsy Product URLs (one per line):")
    url_label.pack(pady=(10, 5))
    
    url_text = scrolledtext.ScrolledText(root, height=15, width=80)
    url_text.pack(pady=5, padx=20, fill=tk.BOTH, expand=True)

    # Search query input
    query_label = tk.Label(root, text="Search Query (for CSV labeling):")
    query_label.pack(pady=(10, 5))
    
    query_entry = tk.Entry(root, width=50)
    query_entry.pack(pady=5)
    query_entry.insert(0, "manual_search")

    # Delay inputs
    delay_frame = tk.Frame(root)
    delay_frame.pack(pady=10)
    
    tk.Label(delay_frame, text="Min Delay (seconds):").grid(row=0, column=0, padx=5)
    min_delay_entry = tk.Entry(delay_frame, width=10)
    min_delay_entry.grid(row=0, column=1, padx=5)
    min_delay_entry.insert(0, "30")
    
    tk.Label(delay_frame, text="Max Delay (seconds):").grid(row=0, column=2, padx=5)
    max_delay_entry = tk.Entry(delay_frame, width=10)
    max_delay_entry.grid(row=0, column=3, padx=5)
    max_delay_entry.insert(0, "60")

    # CSV filename
    csv_label = tk.Label(root, text="Output CSV filename:")
    csv_label.pack(pady=(10, 5))
    
    csv_entry = tk.Entry(root, width=50)
    csv_entry.pack(pady=5)
    csv_entry.insert(0, "etsy_crawl4ai_analysis.csv")

    # Result variables
    result = {'urls': None, 'query': None, 'min_delay': None, 'max_delay': None, 'csv_file': None}

    def on_continue():
        urls_text = url_text.get("1.0", tk.END).strip()
        if not urls_text:
            messagebox.showerror("Error", "Please enter at least one URL.")
            return
        
        urls = [url.strip() for url in urls_text.split('\n') if url.strip()]
        etsy_urls = [url for url in urls if 'etsy.com/listing/' in url]
        
        if not etsy_urls:
            messagebox.showerror("Error", "No valid Etsy product URLs found. URLs must contain 'etsy.com/listing/'")
            return
        
        try:
            min_delay = float(min_delay_entry.get())
            max_delay = float(max_delay_entry.get())
            if min_delay <= 0 or max_delay <= 0 or min_delay > max_delay:
                raise ValueError("Invalid delays")
        except ValueError:
            messagebox.showerror("Error", "Please enter valid delay values.")
            return
        
        query = query_entry.get().strip()
        if not query:
            query = "manual_search"
        
        csv_file = csv_entry.get().strip()
        if not csv_file.endswith('.csv'):
            csv_file += '.csv'
        
        result['urls'] = etsy_urls
        result['query'] = query
        result['min_delay'] = min_delay
        result['max_delay'] = max_delay
        result['csv_file'] = csv_file
        
        messagebox.showinfo("Success", f"Found {len(etsy_urls)} valid Etsy URLs. Starting advanced scraper...")
        root.destroy()

    def on_cancel():
        root.destroy()

    # Buttons
    button_frame = tk.Frame(root)
    button_frame.pack(pady=10)
    
    continue_btn = tk.Button(button_frame, text="Start Scraping", command=on_continue, bg="green", fg="white", font=("Arial", 12))
    continue_btn.pack(side=tk.LEFT, padx=10)
    
    cancel_btn = tk.Button(button_frame, text="Cancel", command=on_cancel, bg="red", fg="white", font=("Arial", 12))
    cancel_btn.pack(side=tk.LEFT, padx=10)

    root.mainloop()
    
    if result['urls']:
        return result['urls'], result['query'], result['min_delay'], result['max_delay'], result['csv_file']
    else:
        return None

async def setup_undetected_browser():
    """Setup undetected browser using patchright."""
    print("🚀 Setting up Undetected Browser (Patchright)...")
    
    try:
        # Launch browser with stealth settings
        browser = await patchright.chromium.launch(
            headless=False,  # Visible browser is less detectable
            args=[
                '--no-first-run',
                '--no-default-browser-check',
                '--disable-extensions',
                '--disable-plugins',
                '--disable-web-security',
                '--allow-running-insecure-content',
                '--disable-sync',
                '--disable-default-apps',
                '--window-size=1920,1080',
                '--start-maximized',
                '--disable-blink-features=AutomationControlled',
                '--disable-features=VizDisplayCompositor',
                '--disable-ipc-flooding-protection',
                '--disable-renderer-backgrounding',
                '--disable-backgrounding-occluded-windows',
                '--disable-client-side-phishing-detection',
                '--no-sandbox',
                '--disable-dev-shm-usage'
            ]
        )
        
        # Create context with additional stealth
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        )
        
        # Add stealth scripts
        await context.add_init_script("""
            // Hide webdriver property
            Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
            
            // Override plugins
            Object.defineProperty(navigator, 'plugins', {
                get: () => [
                    {name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer', description: 'Portable Document Format'},
                    {name: 'Chrome PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai', description: ''},
                    {name: 'Native Client', filename: 'internal-nacl-plugin', description: ''}
                ]
            });
            
            // Override languages
            Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']});
            
            // Override platform
            Object.defineProperty(navigator, 'platform', {get: () => 'Win32'});
            
            // Override hardware
            Object.defineProperty(navigator, 'hardwareConcurrency', {get: () => 8});
            Object.defineProperty(navigator, 'deviceMemory', {get: () => 8});
            Object.defineProperty(navigator, 'maxTouchPoints', {get: () => 0});
            
            // Override chrome runtime
            window.chrome = {
                runtime: {
                    onConnect: undefined,
                    onMessage: undefined
                }
            };
            
            // Remove automation indicators
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
            
            // Mock realistic browser behavior
            Object.defineProperty(navigator, 'vendor', {get: () => 'Google Inc.'});
        """)
        
        page = await context.new_page()
        
        print("✅ Undetected Browser setup complete")
        return browser, context, page
        
    except Exception as e:
        print(f"Error setting up browser: {e}")
        raise

async def add_human_behavior(page):
    """Add realistic human-like behavior to avoid detection"""
    try:
        print("🤖 Simulating human behavior...")
        
        # Random scroll behavior
        scroll_positions = [
            random.randint(100, 300),
            random.randint(300, 600),
            random.randint(600, 1000)
        ]

        for position in scroll_positions:
            await page.evaluate(f"window.scrollTo(0, {position});")
            await asyncio.sleep(random.uniform(1, 3))

        # Random page interactions
        interactions = [
            "document.body.focus();",
            "window.scrollTo(0, 0);",
            f"window.scrollTo(0, {random.randint(200, 800)});",
        ]

        selected_interaction = random.choice(interactions)
        await page.evaluate(selected_interaction)
        await asyncio.sleep(random.uniform(2, 5))

        # Simulate reading time
        reading_time = random.uniform(5, 15)
        print(f"📖 Simulating reading time: {reading_time:.1f} seconds")
        await asyncio.sleep(reading_time)

    except Exception as e:
        print(f"Human behavior simulation error: {e}")

async def check_bot_detection(page):
    """Check if bot detection has been triggered"""
    try:
        content = await page.content()
        title = await page.title()
        
        content_lower = content.lower()
        title_lower = title.lower()

        # Bot detection indicators
        bot_indicators = [
            "access blocked",
            "unusual activity",
            "automated",
            "bot activity",
            "captcha",
            "verify you are human",
            "security check",
            "suspicious activity",
            "please verify",
            "are you a robot",
            "cloudflare"
        ]

        for indicator in bot_indicators:
            if indicator in content_lower or indicator in title_lower:
                print(f"🚨 BOT DETECTION TRIGGERED: {indicator}")
                return True

        return False

    except Exception as e:
        print(f"Error checking bot detection: {e}")
        return False

def extract_product_info_from_html(html_content, product_url, search_query):
    """Extract enhanced product information from HTML content."""
    print(f"📊 Extracting product info from: {product_url}")
    try:
        soup = BeautifulSoup(html_content, 'html.parser')

        # Extract title
        title = 'N/A'
        title_selectors = [
            'h1[data-test-id="listing-page-title"]',
            'h1[class*="listing-page-title"]',
            'h1',
            '[data-test-id="listing-page-title"]'
        ]
        for selector in title_selectors:
            title_elem = soup.select_one(selector)
            if title_elem:
                title = title_elem.get_text(strip=True)
                break

        # Extract price
        price = 'N/A'
        price_selectors = [
            'p[data-test-id="lsp-price"]',
            'span[class*="currency-value"]',
            'p[class*="wt-text-title"]',
            '[class*="price"]'
        ]
        for selector in price_selectors:
            try:
                price_elem = soup.select_one(selector)
                if price_elem:
                    price_text = price_elem.get_text(strip=True)
                    if '$' in price_text:
                        price = price_text
                        break
            except:
                continue

        # Extract image URL
        image_url = 'N/A'
        img_selectors = [
            'img[src*="i.etsystatic.com"]',
            'img[data-test-id="listing-page-image"]',
            'img[class*="listing-page-image"]'
        ]

        for selector in img_selectors:
            try:
                img_elements = soup.select(selector)
                for img_elem in img_elements:
                    if img_elem:
                        src = img_elem.get('src') or img_elem.get('data-src')
                        if src and 'i.etsystatic.com' in src:
                            if not any(x in src.lower() for x in ['icon', 'logo', 'avatar', 'badge', 'star', 'button']):
                                if any(size in src for size in ['794x', '570x', '340x', '270x']):
                                    image_url = src
                                    break
                                elif image_url == 'N/A':
                                    image_url = src
                if image_url != 'N/A':
                    break
            except:
                continue

        return {
            'search_query': search_query,
            'title': title,
            'price': price,
            'product_url': product_url,
            'image_url': image_url,
            'total_reviews': 'N/A',
            'most_recent_review_date': 'N/A',
            'oldest_review_date': 'N/A'
        }
    except Exception as e:
        print(f"Error extracting product info: {e}")
        return None

async def scrape_etsy_crawl4ai_style(product_urls, search_query, min_delay, max_delay, csv_filename):
    """Crawl4AI-inspired Etsy scraper using advanced anti-detection."""
    print("🚀 Starting Crawl4AI-Inspired Etsy Scraper")
    print("INFO: Using advanced anti-detection techniques inspired by Crawl4AI")
    print(f"Search Query: {search_query}")
    print(f"Product URLs: {len(product_urls)}")
    print(f"Delay Between Products: {min_delay}-{max_delay} seconds")
    print(f"Output CSV: {csv_filename}")

    browser = None
    context = None
    page = None
    all_product_data = []
    output_dir = "etsy_crawl4ai_html"

    # Get absolute path for output directory
    script_dir = os.path.dirname(os.path.abspath(__file__)) if '__file__' in locals() else '.'
    output_dir_path = os.path.join(script_dir, output_dir)

    try:
        # --- Setup Undetected Browser ---
        browser, context, page = await setup_undetected_browser()

        # --- Create Output Directory ---
        print(f"📁 Ensuring output directory exists: {output_dir_path}")
        os.makedirs(output_dir_path, exist_ok=True)
        print("✅ Output directory ready.")

        # --- Progressive Enhancement Approach ---
        print("🎯 Starting Progressive Enhancement approach...")

        # Step 1: Visit Google to establish normal browsing pattern
        print("🌐 Step 1: Visiting Google to establish normal browsing pattern...")
        await page.goto("https://www.google.com")
        await asyncio.sleep(random.uniform(5, 10))
        await add_human_behavior(page)

        # Step 2: Visit Etsy homepage
        print("🏠 Step 2: Visiting Etsy homepage...")
        await page.goto("https://www.etsy.com")
        await asyncio.sleep(random.uniform(10, 15))

        if await check_bot_detection(page):
            print("🚨 Bot detection on homepage! Cannot continue.")
            return

        await add_human_behavior(page)

        # --- Process each product URL ---
        for i, url in enumerate(product_urls):
            print(f"\n🎯 Processing product {i + 1}/{len(product_urls)}")
            print(f"URL: {url}")

            try:
                # Progressive session reset
                print("🧹 Performing progressive session reset...")

                # Clear storage
                await context.clear_cookies()
                await page.evaluate("window.localStorage.clear();")
                await page.evaluate("window.sessionStorage.clear();")

                # Visit Google again
                await page.goto("https://www.google.com")
                await asyncio.sleep(random.uniform(8, 12))
                await add_human_behavior(page)

                # Visit Etsy homepage again
                await page.goto("https://www.etsy.com")
                await asyncio.sleep(random.uniform(10, 15))

                if await check_bot_detection(page):
                    print("🚨 Bot detection on homepage! Skipping this product.")
                    continue

                await add_human_behavior(page)

                # Finally visit product page
                print(f"🎯 Navigating to product page...")
                await page.goto(url)

                if await check_bot_detection(page):
                    print("🚨 Bot detection on product page! Skipping.")
                    continue

                # Progressive delay
                delay = random.uniform(min_delay, max_delay)
                print(f"⏳ Progressive delay: {delay:.1f} seconds")
                await asyncio.sleep(delay)

                await add_human_behavior(page)

                # Save HTML
                html_filename = f"crawl4ai_product_{i+1}.html"
                html_filepath = os.path.join(output_dir_path, html_filename)

                print(f"💾 Saving HTML to {html_filepath}...")
                html_content = await page.content()
                with open(html_filepath, 'w', encoding='utf-8') as f:
                    f.write(html_content)

                # Extract product info
                product_info = extract_product_info_from_html(html_content, url, search_query)

                if product_info:
                    all_product_data.append(product_info)
                    print(f"✅ Successfully extracted: {product_info['title'][:50]}...")
                else:
                    print("❌ Could not extract product info")

            except Exception as e:
                print(f"❌ Error processing product {i+1}: {e}")
                continue

        # --- Save to CSV ---
        if all_product_data:
            fieldnames = [
                'search_query', 'title', 'price', 'product_url', 'image_url',
                'total_reviews', 'most_recent_review_date', 'oldest_review_date'
            ]

            with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(all_product_data)

            print(f"\n✅ Successfully saved {len(all_product_data)} products to {csv_filename}")

    except Exception as e:
        print(f"Critical error: {e}")
        messagebox.showerror("Critical Error", f"An error occurred: {e}")

    finally:
        if page:
            await page.close()
        if context:
            await context.close()
        if browser:
            print("🔒 Closing browser...")
            await browser.close()

        print(f"\n🎉 Crawl4AI-Inspired Scraping Complete")
        print(f"Successfully processed {len(all_product_data)} products")
        print(f"HTML files saved to: {output_dir_path}")
        print(f"Data saved to: {csv_filename}")

# --- Run the scraper ---
async def main():
    # Check dependencies
    try:
        import patchright.async_api as patchright
        from bs4 import BeautifulSoup
    except ImportError as e:
        missing_lib = str(e).split("'")[-2]
        messagebox.showerror("Missing Libraries",
                             f"Required library '{missing_lib}' not found.\n"
                             f"Please install: pip install {missing_lib}")
        print(f"ERROR: Missing required library '{missing_lib}'.")
        return

    inputs = get_manual_urls_gui()
    if inputs:
        product_urls, search_query, min_delay, max_delay, csv_filename = inputs
        await scrape_etsy_crawl4ai_style(product_urls, search_query, min_delay, max_delay, csv_filename)
    else:
        print("\nScript cancelled by user.")

if __name__ == "__main__":
    asyncio.run(main())
