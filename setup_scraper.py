#!/usr/bin/env python3
"""
Setup script for Etsy scraper - installs dependencies and checks Chrome driver.
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages."""
    print("📦 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ All packages installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install packages: {e}")
        return False

def check_chrome():
    """Check if Chrome is installed."""
    print("\n🌐 Checking Chrome browser...")
    try:
        # Try to find Chrome executable
        import platform
        system = platform.system()
        
        if system == "Windows":
            chrome_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
            ]
        elif system == "Darwin":  # macOS
            chrome_paths = ["/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"]
        else:  # Linux
            chrome_paths = ["/usr/bin/google-chrome", "/usr/bin/chromium-browser"]
        
        chrome_found = False
        for path in chrome_paths:
            if os.path.exists(path):
                print(f"✅ Chrome found at: {path}")
                chrome_found = True
                break
        
        if not chrome_found:
            print("⚠️  Chrome browser not found in common locations")
            print("   Please install Google Chrome from: https://www.google.com/chrome/")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking Chrome: {e}")
        return False

def check_chromedriver():
    """Check ChromeDriver availability."""
    print("\n🚗 Checking ChromeDriver...")
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        # Try to create a driver instance
        driver = webdriver.Chrome(options=options)
        driver.quit()
        print("✅ ChromeDriver is working correctly")
        return True
        
    except Exception as e:
        print(f"⚠️  ChromeDriver issue: {e}")
        print("   Selenium will try to auto-download ChromeDriver")
        print("   If issues persist, manually download from: https://chromedriver.chromium.org/")
        return True  # Return True as Selenium can auto-manage this

def main():
    """Main setup function."""
    print("🔧 Setting up Etsy Scraper\n")
    
    success = True
    
    # Install requirements
    if not install_requirements():
        success = False
    
    # Check Chrome
    if not check_chrome():
        success = False
    
    # Check ChromeDriver
    if not check_chromedriver():
        success = False
    
    print("\n" + "="*50)
    
    if success:
        print("🎉 Setup completed successfully!")
        print("\n📋 Next steps:")
        print("1. Run: python test_etsy_scraper.py  (to test)")
        print("2. Run: python etsy.py  (to start scraping)")
        print("\n⚠️  Important reminders:")
        print("- Use reasonable delays (8-15 seconds)")
        print("- Respect Etsy's Terms of Service")
        print("- Don't scrape too aggressively")
    else:
        print("❌ Setup encountered some issues")
        print("Please resolve the issues above before running the scraper")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
