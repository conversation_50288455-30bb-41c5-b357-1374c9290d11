#!/usr/bin/env python3
"""
Test script for smart review extraction functionality
Tests the new logic for distinguishing item reviews vs shop reviews
"""

import sys
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# Import our smart review function
from etsy import extract_review_data_smart

def test_smart_review_extraction():
    """Test the smart review extraction on a real Etsy product page"""
    
    print("🧪 Testing Smart Review Extraction")
    print("=" * 50)
    
    # Setup Chrome driver
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    try:
        # Test URL - use a product with reviews
        test_url = "https://www.etsy.com/listing/1234567890/vintage-necklace"  # Replace with actual URL
        
        print(f"🌐 Navigating to test URL...")
        print(f"   URL: {test_url}")
        
        # You can replace this with any Etsy product URL that has reviews
        # For testing, let's use a search result instead
        driver.get("https://www.etsy.com/search?q=vintage+necklace")
        time.sleep(3)
        
        # Click on first product with reviews
        try:
            # Look for products with review indicators
            product_links = driver.find_elements(By.CSS_SELECTOR, "a[href*='/listing/']")
            
            for link in product_links[:3]:  # Try first 3 products
                try:
                    # Check if this product has reviews mentioned
                    parent = link.find_element(By.XPATH, "./..")
                    if "review" in parent.text.lower() or "star" in parent.text.lower():
                        print(f"🎯 Found product with reviews, clicking...")
                        driver.execute_script("arguments[0].click();", link)
                        time.sleep(5)
                        break
                except:
                    continue
            
        except Exception as e:
            print(f"⚠️ Could not find product with reviews: {e}")
            print("📝 Please manually navigate to an Etsy product page with reviews")
            input("Press Enter when ready to test...")
        
        # Now test our smart review extraction
        print("\n🔍 Starting Smart Review Analysis...")
        print("-" * 40)
        
        result = extract_review_data_smart(driver)
        
        print("\n📊 RESULTS:")
        print("=" * 30)
        print(f"Total Reviews: {result['total_reviews']}")
        print(f"Most Recent Review: {result['most_recent_review_date']}")
        print(f"Oldest Review: {result['oldest_review_date']}")
        
        # Validate results
        print("\n✅ VALIDATION:")
        print("-" * 20)
        
        if result['total_reviews'] > 0:
            print("✅ Found reviews successfully")
        else:
            print("❌ No reviews found - check if page has 'Reviews for this item'")
        
        if result['most_recent_review_date']:
            print("✅ Most recent date extracted")
        else:
            print("⚠️ No recent date found")
        
        if result['oldest_review_date']:
            print("✅ Oldest date extracted")
        else:
            print("⚠️ No oldest date found")
        
        # Check if dates make sense
        if result['most_recent_review_date'] and result['oldest_review_date']:
            from datetime import datetime
            recent = datetime.strptime(result['most_recent_review_date'], '%Y-%m-%d')
            oldest = datetime.strptime(result['oldest_review_date'], '%Y-%m-%d')
            
            if recent >= oldest:
                print("✅ Date range is logical (recent >= oldest)")
            else:
                print("❌ Date range issue (recent < oldest)")
        
        print(f"\n🎉 Test completed!")
        
        return result
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return None
        
    finally:
        print("\n🔄 Closing browser...")
        driver.quit()

def manual_test():
    """Manual test where user provides URL"""
    
    print("🧪 Manual Smart Review Test")
    print("=" * 30)
    
    url = input("Enter Etsy product URL to test: ").strip()
    
    if not url:
        print("❌ No URL provided")
        return
    
    # Setup Chrome driver
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    try:
        print(f"🌐 Navigating to: {url}")
        driver.get(url)
        time.sleep(5)
        
        print("🔍 Running smart review extraction...")
        result = extract_review_data_smart(driver)
        
        print("\n📊 RESULTS:")
        print("=" * 30)
        for key, value in result.items():
            print(f"{key}: {value}")
        
        return result
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None
        
    finally:
        driver.quit()

if __name__ == "__main__":
    print("Smart Review Extraction Test")
    print("=" * 40)
    print("1. Automatic test (searches for product)")
    print("2. Manual test (provide URL)")
    
    choice = input("\nChoose test type (1 or 2): ").strip()
    
    if choice == "1":
        test_smart_review_extraction()
    elif choice == "2":
        manual_test()
    else:
        print("Invalid choice")
