#!/usr/bin/env python3
"""
Etsy Scraper using Crawl4AI Docker API
Advanced anti-detection using Crawl4AI's undetected browser capabilities
"""

import asyncio
import aiohttp
import json
import csv
import os
import time
import random
import tkinter as tk
from tkinter import simpledialog, messagebox, scrolledtext
from datetime import datetime
from urllib.parse import urljoin, urlparse
import subprocess
import sys

class Crawl4AIEtsyScraper:
    def __init__(self, base_url="http://localhost:11235"):
        self.base_url = base_url
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def check_docker_running(self):
        """Check if Docker is running and Crawl4AI container is available."""
        try:
            result = subprocess.run(['docker', 'ps'], capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False
    
    def start_crawl4ai_container(self):
        """Start the Crawl4AI Docker container."""
        print("🐳 Starting Crawl4AI Docker container...")
        try:
            # Stop existing container if running
            subprocess.run(['docker', 'stop', 'crawl4ai'], capture_output=True, timeout=30)
            subprocess.run(['docker', 'rm', 'crawl4ai'], capture_output=True, timeout=30)
            
            # Start new container
            cmd = [
                'docker', 'run', '-d',
                '-p', '11235:11235',
                '--name', 'crawl4ai',
                '--shm-size=2g',
                'unclecode/crawl4ai:latest'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            if result.returncode == 0:
                print("✅ Crawl4AI container started successfully")
                print("⏳ Waiting for container to be ready...")
                time.sleep(15)  # Give container time to start
                return True
            else:
                print(f"❌ Failed to start container: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ Timeout starting Docker container")
            return False
        except Exception as e:
            print(f"❌ Error starting container: {e}")
            return False
    
    async def test_connection(self):
        """Test connection to Crawl4AI API."""
        try:
            async with self.session.get(f"{self.base_url}/health", timeout=10) as response:
                if response.status == 200:
                    print("✅ Crawl4AI API is responding")
                    return True
                else:
                    print(f"❌ API returned status {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Cannot connect to Crawl4AI API: {e}")
            return False
    
    async def crawl_url(self, url, delay_before=None):
        """Crawl a single URL using Crawl4AI's advanced anti-detection."""
        if delay_before:
            print(f"⏳ Waiting {delay_before:.1f} seconds before crawling...")
            await asyncio.sleep(delay_before)
        
        print(f"🕷️ Crawling: {url}")
        
        # Crawl4AI configuration with advanced anti-detection
        payload = {
            "urls": [url],
            "browser_config": {
                "type": "BrowserConfig",
                "params": {
                    "headless": False,  # Visible browser is less detectable
                    "viewport": {
                        "type": "dict", 
                        "value": {"width": 1920, "height": 1080}
                    },
                    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36",
                    "extra_args": [
                        "--no-first-run",
                        "--no-default-browser-check", 
                        "--disable-extensions",
                        "--disable-plugins",
                        "--disable-web-security",
                        "--allow-running-insecure-content",
                        "--disable-sync",
                        "--disable-default-apps",
                        "--window-size=1920,1080",
                        "--start-maximized",
                        "--disable-blink-features=AutomationControlled",
                        "--disable-features=VizDisplayCompositor"
                    ]
                }
            },
            "crawler_config": {
                "type": "CrawlerRunConfig", 
                "params": {
                    "cache_mode": "bypass",
                    "page_timeout": 60000,
                    "delay_before_return_html": 5000,  # Wait 5 seconds for page to load
                    "remove_overlay_elements": True,
                    "simulate_user": True,  # Simulate human behavior
                    "override_navigator": True,  # Override navigator properties
                    "magic": True  # Enable Crawl4AI's magic anti-detection
                }
            }
        }
        
        try:
            async with self.session.post(
                f"{self.base_url}/crawl",
                json=payload,
                timeout=120
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    
                    if result.get("success") and result.get("results"):
                        crawl_result = result["results"][0]
                        
                        if crawl_result.get("success"):
                            print(f"✅ Successfully crawled: {url}")
                            return {
                                "success": True,
                                "url": url,
                                "html": crawl_result.get("html", ""),
                                "markdown": crawl_result.get("markdown", ""),
                                "title": crawl_result.get("metadata", {}).get("title", "N/A")
                            }
                        else:
                            error_msg = crawl_result.get("error_message", "Unknown error")
                            print(f"❌ Crawl failed for {url}: {error_msg}")
                            return {"success": False, "url": url, "error": error_msg}
                    else:
                        print(f"❌ No results returned for {url}")
                        return {"success": False, "url": url, "error": "No results"}
                else:
                    error_text = await response.text()
                    print(f"❌ API error {response.status}: {error_text}")
                    return {"success": False, "url": url, "error": f"API error {response.status}"}
                    
        except asyncio.TimeoutError:
            print(f"❌ Timeout crawling {url}")
            return {"success": False, "url": url, "error": "Timeout"}
        except Exception as e:
            print(f"❌ Error crawling {url}: {e}")
            return {"success": False, "url": url, "error": str(e)}
    
    def extract_product_info(self, crawl_result, search_query):
        """Extract product information from crawl result."""
        if not crawl_result.get("success"):
            return None
            
        html = crawl_result.get("html", "")
        markdown = crawl_result.get("markdown", "")
        url = crawl_result.get("url", "")
        
        # Use BeautifulSoup for HTML parsing
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html, 'html.parser')
        except ImportError:
            print("⚠️ BeautifulSoup not available, using basic extraction")
            soup = None
        
        # Extract title
        title = crawl_result.get("title", "N/A")
        if soup and title == "N/A":
            title_selectors = [
                'h1[data-test-id="listing-page-title"]',
                'h1[class*="listing-page-title"]', 
                'h1'
            ]
            for selector in title_selectors:
                title_elem = soup.select_one(selector)
                if title_elem:
                    title = title_elem.get_text(strip=True)
                    break
        
        # Extract price from markdown (often more reliable)
        price = "N/A"
        if markdown:
            import re
            price_patterns = [
                r'\$[\d,]+\.?\d*',
                r'USD [\d,]+\.?\d*',
                r'Price.*?\$[\d,]+\.?\d*'
            ]
            for pattern in price_patterns:
                match = re.search(pattern, markdown)
                if match:
                    price = match.group(0)
                    break
        
        # Extract image URL
        image_url = "N/A"
        if soup:
            img_selectors = [
                'img[src*="i.etsystatic.com"]',
                'img[data-test-id="listing-page-image"]'
            ]
            for selector in img_selectors:
                img_elem = soup.select_one(selector)
                if img_elem:
                    src = img_elem.get('src') or img_elem.get('data-src')
                    if src and 'i.etsystatic.com' in src:
                        image_url = src
                        break
        
        return {
            'search_query': search_query,
            'title': title,
            'price': price,
            'product_url': url,
            'image_url': image_url,
            'total_reviews': 'N/A',
            'most_recent_review_date': 'N/A', 
            'oldest_review_date': 'N/A'
        }

def get_manual_urls_gui():
    """Get product URLs manually from user with Docker setup instructions."""
    root = tk.Tk()
    root.title("Crawl4AI Docker Etsy Scraper")
    root.geometry("900x700")

    # Instructions
    instructions = tk.Label(root, text="""
🐳 CRAWL4AI DOCKER ETSY SCRAPER 🐳

This scraper uses Crawl4AI's Docker container with advanced anti-detection:
• Undetected browser mode with deep patches
• Magic anti-detection algorithms  
• Stealth mode with navigator override
• Human behavior simulation

SETUP REQUIREMENTS:
1. Docker Desktop must be running
2. Internet connection for pulling Crawl4AI image
3. At least 4GB RAM available

INSTRUCTIONS:
1. Make sure Docker Desktop is running
2. Open Etsy in your regular browser
3. Search for products manually
4. Copy product URLs (one per line) below
5. URLs should look like: https://www.etsy.com/listing/123456789/product-name
6. Click 'Start Docker Scraping' when ready

The scraper will automatically:
• Pull the Crawl4AI Docker image if needed
• Start the container with optimal settings
• Use advanced anti-detection techniques
""", justify=tk.LEFT, font=("Arial", 9))
    instructions.pack(pady=10)

    # Text area for URLs
    url_label = tk.Label(root, text="Paste Etsy Product URLs (one per line):")
    url_label.pack(pady=(10, 5))
    
    url_text = scrolledtext.ScrolledText(root, height=12, width=90)
    url_text.pack(pady=5, padx=20, fill=tk.BOTH, expand=True)

    # Configuration inputs
    config_frame = tk.Frame(root)
    config_frame.pack(pady=10)
    
    # Search query
    tk.Label(config_frame, text="Search Query:").grid(row=0, column=0, padx=5, sticky='w')
    query_entry = tk.Entry(config_frame, width=30)
    query_entry.grid(row=0, column=1, padx=5)
    query_entry.insert(0, "docker_crawl4ai_search")
    
    # Delays
    tk.Label(config_frame, text="Min Delay (sec):").grid(row=1, column=0, padx=5, sticky='w')
    min_delay_entry = tk.Entry(config_frame, width=10)
    min_delay_entry.grid(row=1, column=1, padx=5, sticky='w')
    min_delay_entry.insert(0, "45")
    
    tk.Label(config_frame, text="Max Delay (sec):").grid(row=1, column=2, padx=5, sticky='w')
    max_delay_entry = tk.Entry(config_frame, width=10)
    max_delay_entry.grid(row=1, column=3, padx=5, sticky='w')
    max_delay_entry.insert(0, "90")
    
    # CSV filename
    tk.Label(config_frame, text="CSV Output:").grid(row=2, column=0, padx=5, sticky='w')
    csv_entry = tk.Entry(config_frame, width=40)
    csv_entry.grid(row=2, column=1, columnspan=3, padx=5, sticky='w')
    csv_entry.insert(0, "etsy_crawl4ai_docker_results.csv")

    # Result variables
    result = {'urls': None, 'query': None, 'min_delay': None, 'max_delay': None, 'csv_file': None}

    def on_start():
        urls_text = url_text.get("1.0", tk.END).strip()
        if not urls_text:
            messagebox.showerror("Error", "Please enter at least one URL.")
            return
        
        urls = [url.strip() for url in urls_text.split('\n') if url.strip()]
        etsy_urls = [url for url in urls if 'etsy.com/listing/' in url]
        
        if not etsy_urls:
            messagebox.showerror("Error", "No valid Etsy product URLs found.")
            return
        
        try:
            min_delay = float(min_delay_entry.get())
            max_delay = float(max_delay_entry.get())
            if min_delay <= 0 or max_delay <= 0 or min_delay > max_delay:
                raise ValueError("Invalid delays")
        except ValueError:
            messagebox.showerror("Error", "Please enter valid delay values.")
            return
        
        result['urls'] = etsy_urls
        result['query'] = query_entry.get().strip() or "docker_crawl4ai_search"
        result['min_delay'] = min_delay
        result['max_delay'] = max_delay
        result['csv_file'] = csv_entry.get().strip() or "etsy_crawl4ai_docker_results.csv"
        
        if not result['csv_file'].endswith('.csv'):
            result['csv_file'] += '.csv'
        
        messagebox.showinfo("Starting", f"Starting Crawl4AI Docker scraper with {len(etsy_urls)} URLs...")
        root.destroy()

    def on_cancel():
        root.destroy()

    # Buttons
    button_frame = tk.Frame(root)
    button_frame.pack(pady=15)
    
    start_btn = tk.Button(button_frame, text="🐳 Start Docker Scraping", command=on_start, 
                         bg="blue", fg="white", font=("Arial", 12, "bold"))
    start_btn.pack(side=tk.LEFT, padx=10)
    
    cancel_btn = tk.Button(button_frame, text="Cancel", command=on_cancel, 
                          bg="red", fg="white", font=("Arial", 12))
    cancel_btn.pack(side=tk.LEFT, padx=10)

    root.mainloop()
    
    if result['urls']:
        return result['urls'], result['query'], result['min_delay'], result['max_delay'], result['csv_file']
    else:
        return None

async def main():
    """Main function to run the Crawl4AI Docker Etsy scraper."""
    print("🐳 Crawl4AI Docker Etsy Scraper Starting...")

    # Get user input
    inputs = get_manual_urls_gui()
    if not inputs:
        print("❌ Scraping cancelled by user.")
        return

    product_urls, search_query, min_delay, max_delay, csv_filename = inputs

    print(f"\n📋 Scraping Configuration:")
    print(f"   URLs to scrape: {len(product_urls)}")
    print(f"   Search query: {search_query}")
    print(f"   Delay range: {min_delay}-{max_delay} seconds")
    print(f"   Output file: {csv_filename}")

    async with Crawl4AIEtsyScraper() as scraper:
        # Check Docker
        if not scraper.check_docker_running():
            print("❌ Docker is not running!")
            print("Please start Docker Desktop and try again.")
            input("Press Enter to exit...")
            return

        # Start Crawl4AI container
        if not scraper.start_crawl4ai_container():
            print("❌ Failed to start Crawl4AI container!")
            print("Make sure Docker Desktop is running and you have internet access.")
            input("Press Enter to exit...")
            return

        # Test API connection
        if not await scraper.test_connection():
            print("❌ Cannot connect to Crawl4AI API!")
            print("The container may still be starting. Please wait and try again.")
            input("Press Enter to exit...")
            return

        print(f"\n🚀 Starting to scrape {len(product_urls)} Etsy products...")
        print("🔍 Using Crawl4AI's advanced anti-detection capabilities")

        all_product_data = []
        successful_scrapes = 0

        for i, url in enumerate(product_urls):
            print(f"\n📦 Processing product {i + 1}/{len(product_urls)}")
            print(f"🔗 URL: {url}")

            # Random delay between products
            if i > 0:  # No delay before first product
                delay = random.uniform(min_delay, max_delay)
                print(f"⏳ Anti-detection delay: {delay:.1f} seconds")
                await asyncio.sleep(delay)

            # Crawl the URL
            crawl_result = await scraper.crawl_url(url)

            if crawl_result.get("success"):
                # Extract product information
                product_info = scraper.extract_product_info(crawl_result, search_query)

                if product_info:
                    all_product_data.append(product_info)
                    successful_scrapes += 1
                    print(f"✅ Extracted: {product_info['title'][:60]}...")
                    print(f"💰 Price: {product_info['price']}")
                else:
                    print("⚠️ Could not extract product information")
            else:
                error = crawl_result.get("error", "Unknown error")
                print(f"❌ Failed to crawl: {error}")

                # If we get bot detection, add extra delay
                if "captcha" in error.lower() or "blocked" in error.lower():
                    print("🚨 Possible bot detection! Adding extra delay...")
                    await asyncio.sleep(random.uniform(60, 120))

        # Save results to CSV
        if all_product_data:
            print(f"\n💾 Saving {len(all_product_data)} products to {csv_filename}...")

            fieldnames = [
                'search_query', 'title', 'price', 'product_url', 'image_url',
                'total_reviews', 'most_recent_review_date', 'oldest_review_date'
            ]

            try:
                with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(all_product_data)

                print(f"✅ Successfully saved data to {csv_filename}")

                # Show summary
                print(f"\n📊 SCRAPING SUMMARY:")
                print(f"   Total URLs attempted: {len(product_urls)}")
                print(f"   Successful scrapes: {successful_scrapes}")
                print(f"   Success rate: {(successful_scrapes/len(product_urls)*100):.1f}%")
                print(f"   Output file: {csv_filename}")

            except Exception as e:
                print(f"❌ Error saving CSV: {e}")
        else:
            print("❌ No product data was successfully extracted!")
            print("This could indicate:")
            print("   • Etsy's bot detection is still too strong")
            print("   • Network connectivity issues")
            print("   • Invalid URLs provided")

        # Cleanup: Stop the container
        print(f"\n🧹 Cleaning up...")
        try:
            subprocess.run(['docker', 'stop', 'crawl4ai'], capture_output=True, timeout=30)
            print("✅ Crawl4AI container stopped")
        except:
            print("⚠️ Could not stop container (it may stop automatically)")

    print(f"\n🎉 Crawl4AI Docker scraping complete!")
    input("Press Enter to exit...")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n❌ Scraping interrupted by user")
    except Exception as e:
        print(f"\n❌ Critical error: {e}")
        input("Press Enter to exit...")
