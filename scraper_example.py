import time
import random
import os
import csv
import tkinter as tk
from tkinter import simpledialog, messagebox
from pathlib import Path

# --- Use undetected_chromedriver ---
import undetected_chromedriver as uc
from selenium.common.exceptions import WebDriverException, TimeoutException
from bs4 import BeautifulSoup

# --- Function to get inputs via GUI ---
def get_inputs_gui_uc():
    """Gets scraping parameters for undetected_chromedriver session."""
    root = tk.Tk()
    root.withdraw() # Hide the main tkinter window

    base_url_pattern = None
    max_pages = None
    min_delay = None
    max_delay = None
    csv_filename_out = None

    while not base_url_pattern:
        base_url_pattern = simpledialog.askstring("Input Required",
                                                  "Enter the Base URL Pattern (use {} for page number):\n"
                                                  "Example: https://site.com/search/page/{}/",
                                                  parent=root)
        if not base_url_pattern: messagebox.showwarning("Input Needed", "URL pattern cannot be empty.", parent=root)
        elif "{}" not in base_url_pattern: messagebox.showerror("Invalid Pattern", "Pattern must include '{}'...", parent=root); base_url_pattern = None
        elif not base_url_pattern.startswith(('http://', 'https://')): messagebox.showwarning("Invalid URL", "URL must start with http:// or https://", parent=root); base_url_pattern = None

    while max_pages is None:
        try:
            max_pages_str = simpledialog.askstring("Input Required", "Enter Max Number of Pages to Scrape:", parent=root)
            if max_pages_str is None: return None # User cancelled
            max_pages = int(max_pages_str)
            if max_pages <= 0: messagebox.showwarning("Invalid Input", "Maximum pages must be positive.", parent=root); max_pages = None
        except ValueError: messagebox.showerror("Invalid Input", "Please enter a valid number.", parent=root)

    # Delay here is BETWEEN page navigations within the SAME browser
    while min_delay is None or max_delay is None:
        try:
            min_delay_str = simpledialog.askstring("Input Required", "Enter Min Delay BETWEEN Pages (secs, suggest 30+):", initialvalue="30", parent=root)
            if min_delay_str is None: return None
            min_delay = float(min_delay_str)
            max_delay_str = simpledialog.askstring("Input Required", "Enter Max Delay BETWEEN Pages (secs, suggest 90+):", initialvalue="90", parent=root)
            if max_delay_str is None: return None
            max_delay = float(max_delay_str)

            if min_delay < 5 or max_delay < 5: # Enforce reasonably long delay
                 messagebox.showwarning("Suggest Longer Delay", "Longer delays (e.g., 30-90s) recommended to avoid detection.", parent=root)
            if min_delay <= 0 or max_delay <=0:
                 messagebox.showwarning("Invalid Input", "Delays must be positive.", parent=root); min_delay, max_delay = None, None
            elif min_delay > max_delay:
                 messagebox.showwarning("Invalid Input", "Minimum delay cannot be greater than maximum.", parent=root); min_delay, max_delay = None, None
        except ValueError: messagebox.showerror("Invalid Input", "Please enter valid numbers for delays.", parent=root); min_delay, max_delay = None, None

    while not csv_filename_out:
         default_name = "scraped_direct_bidfax_listings.csv"
         csv_filename_out = simpledialog.askstring("Input Required", "Enter Output CSV Filename:", initialvalue=default_name, parent=root)
         if not csv_filename_out: messagebox.showwarning("Input Needed", "CSV filename cannot be empty.", parent=root)
         elif not csv_filename_out.lower().endswith(".csv"): csv_filename_out += ".csv" # Ensure .csv extension

    root.destroy()
    return base_url_pattern, max_pages, min_delay, max_delay, csv_filename_out
# --- End GUI Function ---


# --- HTML Handling Logic ---
# We will save raw HTML content instead of parsing specific data fields.
# --- End HTML Handling Logic ---


# --- Main Scraping Function ---
def scrape_direct_with_uc(base_url_pattern, max_pages, min_delay, max_delay, csv_filename):
    """
    Uses undetected_chromedriver in a single session, adds delays, clears cookies,
    parses directly and writes to CSV.
    """
    print("--- Starting Direct Scrape with Undetected Chromedriver ---")
    print("INFO: Using single browser session with longer delays.")
    print(f"URL Pattern: {base_url_pattern}")
    print(f"Max Pages: {max_pages}")
    print(f"Delay Between Pages: {min_delay}-{max_delay} seconds")
    print(f"Output CSV: {csv_filename}")

    total_pages_saved = 0
    driver = None
    output_dir = "scraped_html" # Directory to save HTML files

    # Get absolute path for output directory
    script_dir = os.path.dirname(os.path.abspath(__file__)) if '__file__' in locals() else '.'
    output_dir_path = os.path.join(script_dir, output_dir)

    try:
        # --- Setup Undetected ChromeDriver ---
        print("Setting up Undetected ChromeDriver...")
        options = uc.ChromeOptions()
        # You can add options here if needed, e.g.:
        # options.add_argument('--headless') # Run without visible browser window
        # options.add_argument('--incognito')
        # uc tries to handle many anti-detection things automatically

        driver = uc.Chrome(options=options, version_main=None) # Let uc find appropriate Chrome version
        driver.set_page_load_timeout(90) # Timeout for page loads
        print("WebDriver setup complete.")
        # --- End WebDriver Setup ---

        # --- Create Output Directory ---
        print(f"Ensuring output directory exists: {output_dir_path}")
        os.makedirs(output_dir_path, exist_ok=True)
        print("Output directory ready.")
        # --- End Output Directory Setup ---

        # --- Loop Through Pages ---
        for page_num in range(1, max_pages + 1):
            page_url = base_url_pattern.format(page_num)
            print(f"\n--- Processing Page {page_num}/{max_pages} ---")
            print(f"URL: {page_url}")

            try:
                # --- Navigate ---
                print("Navigating...")
                driver.get(page_url)
                print("Navigation complete. Waiting for page load and delay...")
                # ---

                # --- Apply Main Delay ---
                page_delay = random.uniform(min_delay, max_delay)
                print(f"Waiting for {page_delay:.2f} seconds...")
                time.sleep(page_delay)
                # ---

                # --- Check for Blocks ---
                print("Checking for Cloudflare blocks...")
                page_title = driver.title
                page_source_sample = driver.page_source[:1000] # Check beginning of source
                if "Sorry, you have been blocked" in page_source_sample or \
                   "Attention Required" in page_title or \
                   "Cloudflare" in page_title: # Add other potential titles/text
                     print(f"ERROR: Cloudflare block detected on page {page_num}. Stopping script.")
                     messagebox.showerror("Cloudflare Block", f"Blocked by Cloudflare when trying to access page {page_num}.\nCannot continue.", parent=tk._default_root)
                     break # Stop the whole process
                print("No immediate block detected.")
                # ---

                # --- Get Source & Save HTML ---
                print("Getting page source...")
                html_content = driver.page_source
                html_filename = f"page_{page_num}.html"
                html_filepath = os.path.join(output_dir_path, html_filename)

                print(f"Saving HTML to {html_filepath}...")
                with open(html_filepath, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                print(f"Successfully saved HTML for page {page_num}.")
                total_pages_saved += 1
                # ---

                # --- Clear Cookies Before Next Page---
                print("Clearing cookies...")
                driver.delete_all_cookies()
                time.sleep(random.uniform(1, 3)) # Short pause after clearing cookies
                # ---

            except TimeoutException:
                 print(f"ERROR: Page {page_num} ({page_url}) timed out.")
                 error_choice = messagebox.askyesno("Timeout Error", f"Page {page_num} took too long to load.\n\nContinue to next page?", parent=tk._default_root)
                 if not error_choice: print("Stopping script due to timeout."); break
                 else: print("Skipping to next page...")
            except WebDriverException as e:
                 print(f"ERROR: WebDriver error on page {page_num}: {e}")
                 error_choice = messagebox.askyesno("WebDriver Error", f"A browser error occurred:\n{e}\n\nTry to continue?", parent=tk._default_root)
                 if not error_choice: print("Stopping script due to WebDriver error."); break
                 else: print("Attempting to continue (browser state might be unstable)...")
            except Exception as e:
                 print(f"ERROR: Unexpected error processing page {page_num}: {e}")
                 error_choice = messagebox.askyesno("Processing Error", f"An unexpected error occurred processing page {page_num}.\nError: {e}\n\nContinue to next page?", parent=tk._default_root)
                 if not error_choice: print("Stopping script due to error."); break
                 else: print("Skipping to next page...")

        # --- End Page Loop ---

    except ImportError:
         # This should ideally not happen if the check at the start passes
         messagebox.showerror("Missing Library", "Failed to import 'undetected_chromedriver'.\nPlease run: pip install undetected-chromedriver", parent=tk._default_root)
         print("ERROR: Could not import undetected_chromedriver.")
    except WebDriverException as e: # Catch setup errors
         messagebox.showerror("WebDriver Setup Error", f"Failed to start Undetected ChromeDriver:\n{e}\n\nCheck Chrome installation.", parent=tk._default_root)
         print(f"WebDriver setup failed: {e}")
    except Exception as e:
         messagebox.showerror("Unexpected Error", f"An critical unexpected error occurred:\n{e}", parent=tk._default_root)
         print(f"An critical unexpected error occurred: {e}")
    finally:
        # --- Close Browser ---
        if driver:
            print("Closing browser...")
            driver.quit()
            print("Browser closed.")
        # ---
        print(f"\n--- Direct Scraping Finished ---")
        print(f"Total HTML pages saved to {output_dir_path}: {total_pages_saved}")

# --- Run the scraper ---
if __name__ == "__main__":
    # Check dependency first
    try:
        import undetected_chromedriver as uc
        from bs4 import BeautifulSoup
    except ImportError as e:
         missing_lib = str(e).split("'")[-2] # Try to get module name
         messagebox.showerror("Missing Libraries", f"Required library '{missing_lib}' not found.\nPlease install it (e.g., pip install {missing_lib})", parent=tk._default_root)
         print(f"ERROR: Missing required library '{missing_lib}'. Please install.")
         exit()

    inputs = get_inputs_gui_uc()
    if inputs:
        url_p, pages, min_d, max_d, csv_f = inputs
        scrape_direct_with_uc(url_p, pages, min_d, max_d, csv_f)
    else:
        print("\nScript cancelled by user.")
