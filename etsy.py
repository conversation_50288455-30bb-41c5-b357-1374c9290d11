import time
import random
import os
import csv
import tkinter as tk
from tkinter import simpledialog, messagebox
from pathlib import Path
from datetime import datetime
import re
from urllib.parse import urljoin, urlparse
import json
import base64

# --- Use undetected_chromedriver ---
import undetected_chromedriver as uc
from selenium.common.exceptions import WebDriverException, TimeoutException, NoSuchElementException
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup

def add_human_behavior(driver):
    """Add realistic human-like behavior to avoid detection"""
    try:
        # Random scroll behavior (safer than mouse movements)
        scroll_positions = [
            random.randint(100, 300),
            random.randint(300, 600),
            random.randint(600, 1000)
        ]

        for position in scroll_positions:
            driver.execute_script(f"window.scrollTo(0, {position});")
            time.sleep(random.uniform(0.5, 1.5))

        # Random page interactions
        interactions = [
            "document.body.focus();",
            "window.scrollTo(0, 0);",
            f"window.scrollTo(0, {random.randint(200, 800)});",
            "document.documentElement.click();"
        ]

        selected_interaction = random.choice(interactions)
        driver.execute_script(selected_interaction)
        time.sleep(random.uniform(0.5, 2.0))

        # Simulate reading time
        reading_time = random.uniform(2, 6)
        time.sleep(reading_time)

    except Exception as e:
        print(f"Human behavior simulation error: {e}")

def random_delay(min_seconds=10, max_seconds=20):
    """Random delay with human-like patterns"""
    delay = random.uniform(min_seconds, max_seconds)
    print(f"⏳ Human-like delay: {delay:.1f} seconds")
    time.sleep(delay)

def check_bot_detection(driver):
    """Check if bot detection has been triggered"""
    try:
        page_source = driver.page_source.lower()
        page_title = driver.title.lower()

        # Bot detection indicators
        bot_indicators = [
            "access blocked",
            "unusual activity",
            "automated",
            "bot activity",
            "captcha",
            "verify you are human",
            "security check",
            "suspicious activity",
            "please verify",
            "are you a robot",
            "cloudflare"
        ]

        for indicator in bot_indicators:
            if indicator in page_source or indicator in page_title:
                print(f"🚨 BOT DETECTION TRIGGERED: {indicator}")
                return True

        return False

    except Exception as e:
        print(f"Error checking bot detection: {e}")
        return False

# --- Function to get Etsy inputs via GUI ---
def get_etsy_inputs_gui():
    """Gets Etsy-specific scraping parameters."""
    root = tk.Tk()
    root.withdraw() # Hide the main tkinter window

    search_query = None
    max_products = None
    min_delay = None
    max_delay = None
    csv_filename_out = None

    # Get search query
    while not search_query:
        search_query = simpledialog.askstring("Etsy Search",
                                              "Enter Etsy search terms:\n"
                                              "Example: vintage rings, handmade jewelry",
                                              parent=root)
        if not search_query:
            messagebox.showwarning("Input Needed", "Search query cannot be empty.", parent=root)

    # Get max products to scrape
    while max_products is None:
        try:
            max_products_str = simpledialog.askstring("Input Required", "Enter Max Number of Products to Scrape:", parent=root)
            if max_products_str is None: return None # User cancelled
            max_products = int(max_products_str)
            if max_products <= 0: messagebox.showwarning("Invalid Input", "Maximum products must be positive.", parent=root); max_products = None
        except ValueError: messagebox.showerror("Invalid Input", "Please enter a valid number.", parent=root)

    # Get delays
    while min_delay is None or max_delay is None:
        try:
            min_delay_str = simpledialog.askstring("Input Required", "Enter Min Delay BETWEEN Products (secs, suggest 60+):", initialvalue="60", parent=root)
            if min_delay_str is None: return None
            min_delay = float(min_delay_str)
            max_delay_str = simpledialog.askstring("Input Required", "Enter Max Delay BETWEEN Products (secs, suggest 120+):", initialvalue="120", parent=root)
            if max_delay_str is None: return None
            max_delay = float(max_delay_str)

            if min_delay < 30 or max_delay < 45:
                messagebox.showwarning("Suggest Longer Delay", "Longer delays (e.g., 60-120s) strongly recommended to avoid detection. Etsy has extremely strong anti-bot measures.", parent=root)
            if min_delay <= 0 or max_delay <=0:
                messagebox.showwarning("Invalid Input", "Delays must be positive.", parent=root); min_delay, max_delay = None, None
            elif min_delay > max_delay:
                messagebox.showwarning("Invalid Input", "Minimum delay cannot be greater than maximum.", parent=root); min_delay, max_delay = None, None
        except ValueError: messagebox.showerror("Invalid Input", "Please enter valid numbers for delays.", parent=root); min_delay, max_delay = None, None

    # Get output filename
    while not csv_filename_out:
        default_name = f"etsy_{search_query.replace(' ', '_')}_analysis.csv"
        csv_filename_out = simpledialog.askstring("Input Required", "Enter Output CSV Filename:", initialvalue=default_name, parent=root)
        if not csv_filename_out: messagebox.showwarning("Input Needed", "CSV filename cannot be empty.", parent=root)
        elif not csv_filename_out.lower().endswith(".csv"): csv_filename_out += ".csv"

    root.destroy()
    return search_query, max_products, min_delay, max_delay, csv_filename_out

def setup_etsy_chrome_driver():
    """Setup undetected ChromeDriver for Etsy scraping."""
    print("Setting up Undetected ChromeDriver...")

    try:
        options = uc.ChromeOptions()

        # Basic options for better stealth
        options.add_argument('--no-first-run')
        options.add_argument('--no-default-browser-check')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-plugins')
        options.add_argument('--disable-web-security')
        options.add_argument('--allow-running-insecure-content')
        options.add_argument('--disable-sync')
        options.add_argument('--disable-default-apps')

        # Realistic window size
        options.add_argument('--window-size=1920,1080')
        options.add_argument('--start-maximized')

        # Optional: Run headless (comment out to see browser)
        # options.add_argument('--headless')

        # Advanced preferences
        prefs = {
            "profile.default_content_setting_values": {
                "notifications": 2,
                "geolocation": 2,
                "media_stream": 2,
            },
            "profile.managed_default_content_settings": {
                "images": 1  # Allow images for more realistic behavior
            },
            "profile.default_content_settings": {
                "popups": 0
            },
            "profile.password_manager_enabled": False,
            "credentials_enable_service": False,
        }
        options.add_experimental_option("prefs", prefs)

        # Let undetected_chromedriver handle most anti-detection automatically
        driver = uc.Chrome(options=options, version_main=None)
        driver.set_page_load_timeout(120)

        print("✅ Undetected ChromeDriver setup complete")
        return driver

    except Exception as e:
        print(f"Error setting up ChromeDriver: {e}")
        raise

def extract_product_info_from_html(html_content, product_url, search_query):
    """Extract enhanced product information from HTML content."""
    print(f"Extracting product info from: {product_url}")
    try:
        soup = BeautifulSoup(html_content, 'html.parser')

        # Extract title - Updated selector for current Etsy structure
        title = 'N/A'
        title_selectors = [
            'h1[data-test-id="listing-page-title"]',
            'h1[class*="listing-page-title"]',
            'h1',
            '[data-test-id="listing-page-title"]'
        ]
        for selector in title_selectors:
            title_elem = soup.select_one(selector)
            if title_elem:
                title = title_elem.get_text(strip=True)
                break

        # Extract price - Enhanced price extraction
        price = 'N/A'
        price_selectors = [
            'p[data-test-id="lsp-price"]',
            'span[class*="currency-value"]',
            'p[class*="wt-text-title"]',
            '[class*="price"]',
            'p:contains("$")',
            'span:contains("$")'
        ]
        for selector in price_selectors:
            try:
                price_elem = soup.select_one(selector)
                if price_elem:
                    price_text = price_elem.get_text(strip=True)
                    if '$' in price_text:
                        price = price_text
                        break
            except:
                continue

        # Extract image URL - Enhanced image extraction based on current Etsy structure
        image_url = 'N/A'
        img_selectors = [
            'img[alt*="May include"]',  # Main product image
            'img[alt*="product image"]',  # Thumbnail images
            'img[src*="i.etsystatic.com"]',  # Any Etsy static image
            'img[data-test-id="listing-page-image"]',
            'img[class*="listing-page-image"]',
            'div[class*="image-carousel"] img',
            'div[class*="listing-page-image"] img'
        ]

        for selector in img_selectors:
            try:
                img_elements = soup.select(selector)
                for img_elem in img_elements:
                    if img_elem:
                        src = img_elem.get('src') or img_elem.get('data-src')
                        if src and 'i.etsystatic.com' in src:
                            # Filter for actual product images (exclude icons, logos, etc.)
                            if not any(x in src.lower() for x in ['icon', 'logo', 'avatar', 'badge', 'star', 'button']):
                                # Prefer larger images (look for dimensions in URL)
                                if any(size in src for size in ['794x', '570x', '340x', '270x']):
                                    image_url = src
                                    break
                                elif image_url == 'N/A':  # Take any valid image if no sized one found
                                    image_url = src
                if image_url != 'N/A':
                    break
            except:
                continue

        return {
            'search_query': search_query,
            'title': title,
            'price': price,
            'product_url': product_url,
            'image_url': image_url
        }
    except Exception as e:
        print(f"Error extracting product info: {e}")
        return None

def extract_review_data(driver):
    """
    Extracts review data by first determining the page layout (new vs. old)
    and then applying the correct scraping logic.
    """
    print("Analyzing reviews for the current page...")
    
    # --- STEP 1: Determine the page layout by checking for reviews section ---
    is_new_layout = False
    try:
        # Look for reviews section on the current page first
        reviews_section = driver.find_element(By.CSS_SELECTOR, '[data-test-id="reviews-section"], #reviews')
        print("Found reviews section on current page.")

        # Try to find "See all reviews" or similar link
        try:
            all_reviews_link = WebDriverWait(driver, 3).until(
                EC.element_to_be_clickable((By.PARTIAL_LINK_TEXT, "See all reviews"))
            )
            print("Found 'See all reviews' link. Navigating to reviews page...")
            driver.execute_script("arguments[0].click();", all_reviews_link)
            time.sleep(random.uniform(3, 5))
            is_new_layout = True
        except TimeoutException:
            print("No 'See all reviews' link found. Reviews are on this page.")
    except:
        print("Could not find reviews section. May need to scroll or reviews may not be available.")

    # --- STEP 2: Set selectors for current Etsy structure ---
    # Updated selectors based on current Etsy review structure
    review_card_selectors = [
        "div[data-test-id='review']",
        "div[class*='review']",
        "div[data-review-id]",
        "div.wt-grid.wt-grid--block.wt-mb-xs-5"
    ]

    date_selectors = [
        'p[data-test-id="review-date"]',
        'span[class*="review-date"]',
        'p.wt-text-caption.wt-text-truncate',
        'p.wt-text-body-01'
    ]

    next_button_xpaths = [
        "//a[contains(@aria-label, 'Next page')]",
        "//button[contains(@aria-label, 'Next page')]",
        "//a[contains(@class, 'wt-btn--icon') and @aria-label='Next page']",
        "//div[@data-reviews-pagination]//a[.//span[contains(text(), 'Next page')]]"
    ]

    # Optional: Try to sort by "Most recent" on the old layout
    try:
        sort_trigger = WebDriverWait(driver, 5).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, "button.sort-reviews-trigger"))
        )
        driver.execute_script("arguments[0].click();", sort_trigger)
        time.sleep(1)
        most_recent_option = WebDriverWait(driver, 5).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, "button[data-sort-option='Recency']"))
        )
        driver.execute_script("arguments[0].click();", most_recent_option)
        print("✓ Set sorting to Most Recent")
        time.sleep(random.uniform(3, 5))
    except Exception:
        print("Could not set sorting. Proceeding with default sort.")

    # --- STEP 3: Scrape reviews using a unified loop with the correct selectors ---
    review_dates = []
    page_count = 1
    
    while True:
        print(f"Scraping reviews page {page_count}...")
        try:
            # Try to find review cards using multiple selectors
            review_cards = []
            for selector in review_card_selectors:
                try:
                    WebDriverWait(driver, 5).until(
                        EC.presence_of_all_elements_located((By.CSS_SELECTOR, selector))
                    )
                    review_cards = driver.find_elements(By.CSS_SELECTOR, selector)
                    if review_cards:
                        print(f"Found {len(review_cards)} reviews on page {page_count} using selector: {selector}")
                        break
                except TimeoutException:
                    continue

            if not review_cards:
                print("No review cards found, ending review scrape for this product.")
                break

            for card in review_cards:
                # Try multiple date selectors
                date_found = False
                for date_selector in date_selectors:
                    try:
                        date_elem = card.find_element(By.CSS_SELECTOR, date_selector)
                        date_text = date_elem.text.strip()
                        if date_text:
                            review_dates.append(date_text)
                            date_found = True
                            break
                    except NoSuchElementException:
                        continue

                if not date_found:
                    # Fallback: try to find any text that looks like a date
                    try:
                        card_text = card.text
                        # Look for date patterns like "Sep 20, 2025"
                        import re
                        date_pattern = r'[A-Za-z]{3}\s+\d{1,2},\s+\d{4}'
                        dates_in_text = re.findall(date_pattern, card_text)
                        if dates_in_text:
                            review_dates.extend(dates_in_text)
                    except:
                        continue

            # Find and click the 'Next' button using multiple XPaths
            next_button_found = False
            for xpath in next_button_xpaths:
                try:
                    next_button = driver.find_element(By.XPATH, xpath)
                    # Check if button is disabled
                    button_class = next_button.get_attribute('class') or ''
                    aria_disabled = next_button.get_attribute('aria-disabled')

                    if 'wt-is-disabled' in button_class or aria_disabled == 'true':
                        print("'Next' button is disabled. Reached the end of reviews.")
                        next_button_found = False
                        break

                    print(f"Found 'Next' button using xpath: {xpath}, clicking to next page...")
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", next_button)
                    time.sleep(1)
                    driver.execute_script("arguments[0].click();", next_button)
                    time.sleep(random.uniform(3, 6))
                    page_count += 1
                    next_button_found = True
                    break
                except NoSuchElementException:
                    continue

            if not next_button_found:
                print("No 'Next' button found, reached the end of reviews.")
                break

        except TimeoutException:
            print("Timed out waiting for review cards to load. Assuming no more reviews.")
            break

    # --- STEP 4: Process the collected dates (this part is unchanged) ---
    most_recent = 'N/A'
    oldest = 'N/A'
    
    if review_dates:
        parsed_dates = []
        for date_str in review_dates:
            try:
                parsed_dates.append(datetime.strptime(date_str, '%b %d, %Y'))
            except ValueError:
                continue
        if parsed_dates:
            parsed_dates.sort()
            oldest = parsed_dates[0].strftime('%Y-%m-%d')
            most_recent = parsed_dates[-1].strftime('%Y-%m-%d')
    
    return {
        'total_reviews': len(review_dates),
        'most_recent_review_date': most_recent,
        'oldest_review_date': oldest,
    }

def extract_review_data_smart(driver):
    """
    Smart review extraction strategy based on user requirements:
    1. Identify "Reviews for this item" vs "Reviews for this shop"
    2. Sort by Most Recent
    3. Calculate average reviews per page
    4. Jump to highest page number
    5. Navigate to final page
    6. Calculate total: (Pages-1) × Average + Last Page Count
    """
    print("Smart review analysis starting...")

    try:
        # Wait for page to load
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )

        # --- STEP 1: Identify and navigate to "Reviews for this item" ---
        print("Step 1: Looking for 'Reviews for this item' section...")

        # First, scroll down to load all content
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight/2);")
        time.sleep(2)
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(3)

        # Look for "Reviews for this item" section specifically
        item_reviews_found = False
        item_review_selectors = [
            "//h3[contains(text(), 'Reviews for this item')]",
            "//h2[contains(text(), 'Reviews for this item')]",
            "//div[contains(text(), 'Reviews for this item')]",
            "//span[contains(text(), 'Reviews for this item')]",
            "//button[contains(text(), 'Reviews for this item')]"
        ]

        for selector in item_review_selectors:
            try:
                item_review_section = driver.find_element(By.XPATH, selector)
                if item_review_section:
                    print("✅ Found 'Reviews for this item' section")
                    # Scroll to this section
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", item_review_section)
                    time.sleep(2)
                    item_reviews_found = True
                    break
            except:
                continue

        # FALLBACK: If "Reviews for this item" section not found, look for any reviews with "This item" indicator
        if not item_reviews_found:
            print("⚠️ 'Reviews for this item' section not visible. Using fallback method...")
            print("🔍 Looking for reviews with 'This item' indicator...")

            # Try to find any review cards that contain "This item" text
            fallback_selectors = [
                "//div[contains(@class, 'review') and contains(., 'This item')]",
                "//div[contains(., 'This item') and contains(., 'review')]",
                "//div[contains(., 'This item') and contains(., 'Recommends')]"
            ]

            for selector in fallback_selectors:
                try:
                    fallback_reviews = driver.find_elements(By.XPATH, selector)
                    if fallback_reviews:
                        print(f"✅ Found {len(fallback_reviews)} reviews with 'This item' indicator")
                        item_reviews_found = True
                        break
                except:
                    continue

        if not item_reviews_found:
            print("❌ Could not find any item reviews (neither section nor individual reviews)")
            return {
                'total_reviews': 0,
                'most_recent_review_date': None,
                'oldest_review_date': None
            }

        # --- STEP 2: Set sorting to "Most Recent" ---
        print("Step 2: Setting sort to 'Most Recent'...")

        try:
            # Look for sort dropdown
            sort_selectors = [
                "button[aria-label*='Sort']",
                "select[aria-label*='Sort']",
                "//button[contains(text(), 'Suggested')]",
                "//button[contains(text(), 'Most recent')]",
                "//select//option[contains(text(), 'Most recent')]"
            ]

            for selector in sort_selectors:
                try:
                    if selector.startswith('//'):
                        sort_element = driver.find_element(By.XPATH, selector)
                    else:
                        sort_element = driver.find_element(By.CSS_SELECTOR, selector)

                    if sort_element:
                        driver.execute_script("arguments[0].click();", sort_element)
                        time.sleep(1)

                        # Look for "Most recent" option
                        try:
                            most_recent_option = driver.find_element(By.XPATH, "//button[contains(text(), 'Most recent')] | //option[contains(text(), 'Most recent')]")
                            driver.execute_script("arguments[0].click();", most_recent_option)
                            print("✅ Set sorting to 'Most Recent'")
                            time.sleep(3)
                            break
                        except:
                            continue
                except:
                    continue
        except Exception as e:
            print(f"⚠️ Could not set sorting: {e}")

        # --- STEP 3: Extract reviews from first page and calculate average ---
        print("Step 3: Analyzing first page reviews...")

        # Find review cards specifically for this item
        review_card_selectors = [
            "div[data-test-id='review']",
            "div[class*='review'][class*='item']",
            "div[data-review-id]",
            "//div[contains(@class, 'review') and .//span[contains(text(), 'This item')]]"
        ]

        review_cards = []
        for selector in review_card_selectors:
            try:
                if selector.startswith('//'):
                    cards = driver.find_elements(By.XPATH, selector)
                else:
                    cards = driver.find_elements(By.CSS_SELECTOR, selector)

                if cards:
                    # Filter for item reviews only (look for "This item" indicator)
                    item_cards = []
                    for card in cards:
                        if "This item" in card.text or "Recommends" in card.text:
                            item_cards.append(card)

                    if item_cards:
                        review_cards = item_cards
                        print(f"✅ Found {len(review_cards)} item review cards on first page")
                        break
            except:
                continue

        if not review_cards:
            print("❌ No item review cards found")
            return {
                'total_reviews': 0,
                'most_recent_review_date': None,
                'oldest_review_date': None
            }

        # Calculate average reviews per page
        reviews_per_page = len(review_cards)
        print(f"📊 Average reviews per page: {reviews_per_page}")

        # Extract dates from first page
        date_patterns = [
            r'(\w{3}\s+\d{1,2},\s+\d{4})',  # "Sep 21, 2025"
            r'(\d{1,2}/\d{1,2}/\d{4})',     # "9/21/2025"
            r'(\d{4}-\d{2}-\d{2})'          # "2025-09-21"
        ]

        first_page_dates = []
        for card in review_cards[:3]:  # Check first 3 for speed
            try:
                card_text = card.text
                for pattern in date_patterns:
                    matches = re.findall(pattern, card_text)
                    if matches:
                        for match in matches:
                            try:
                                if ',' in match:
                                    date_obj = datetime.strptime(match, '%b %d, %Y')
                                elif '/' in match:
                                    date_obj = datetime.strptime(match, '%m/%d/%Y')
                                elif '-' in match:
                                    date_obj = datetime.strptime(match, '%Y-%m-%d')
                                else:
                                    continue

                                first_page_dates.append(date_obj)
                            except:
                                continue
            except:
                continue

        most_recent_date = max(first_page_dates) if first_page_dates else None
        print(f"📅 Most recent review: {most_recent_date.strftime('%Y-%m-%d') if most_recent_date else 'N/A'}")

        # --- STEP 4: Find pagination and jump to highest page ---
        print("Step 4: Finding pagination and jumping to last page...")

        total_pages = 1
        last_page_reviews = 0
        oldest_date = None

        try:
            # Look for pagination
            pagination_selectors = [
                "nav[aria-label*='pagination']",
                "div[class*='pagination']",
                "[class*='page-number']"
            ]

            pagination_found = False
            for selector in pagination_selectors:
                try:
                    pagination = driver.find_element(By.CSS_SELECTOR, selector)
                    if pagination:
                        # Find all page number links
                        page_links = pagination.find_elements(By.TAG_NAME, "a")
                        page_numbers = []

                        for link in page_links:
                            try:
                                if link.text.isdigit():
                                    page_numbers.append(int(link.text))
                            except:
                                continue

                        if page_numbers:
                            total_pages = max(page_numbers)
                            print(f"📄 Found pagination: {total_pages} pages total")

                            # Jump to the highest page number
                            for link in reversed(page_links):
                                try:
                                    if link.text == str(total_pages):
                                        print(f"🚀 Jumping to page {total_pages}")
                                        driver.execute_script("arguments[0].click();", link)
                                        time.sleep(4)
                                        pagination_found = True
                                        break
                                except:
                                    continue
                        break
                except:
                    continue

            # --- STEP 5: Navigate to the very last page ---
            if pagination_found:
                print("Step 5: Navigating to final page...")

                # Keep clicking "Next" until no more pages
                max_iterations = 10  # Safety limit
                iteration = 0

                while iteration < max_iterations:
                    try:
                        # Look for "Next" button
                        next_selectors = [
                            "//a[contains(@aria-label, 'Next page')]",
                            "//button[contains(@aria-label, 'Next page')]",
                            "//a[contains(text(), 'Next')]"
                        ]

                        next_found = False
                        for selector in next_selectors:
                            try:
                                next_button = driver.find_element(By.XPATH, selector)
                                if next_button and next_button.is_enabled():
                                    print(f"➡️ Clicking Next (iteration {iteration + 1})")
                                    driver.execute_script("arguments[0].click();", next_button)
                                    time.sleep(3)
                                    next_found = True
                                    break
                            except:
                                continue

                        if not next_found:
                            print("✅ Reached final page (no Next button)")
                            break

                        iteration += 1
                    except:
                        break

                # --- STEP 6: Extract data from final page ---
                print("Step 6: Extracting data from final page...")

                # Count reviews on last page
                final_page_cards = []
                for selector in review_card_selectors:
                    try:
                        if selector.startswith('//'):
                            cards = driver.find_elements(By.XPATH, selector)
                        else:
                            cards = driver.find_elements(By.CSS_SELECTOR, selector)

                        if cards:
                            # Filter for item reviews
                            item_cards = []
                            for card in cards:
                                if "This item" in card.text or "Recommends" in card.text:
                                    item_cards.append(card)

                            if item_cards:
                                final_page_cards = item_cards
                                break
                    except:
                        continue

                last_page_reviews = len(final_page_cards)
                print(f"📊 Reviews on final page: {last_page_reviews}")

                # Extract oldest date from final page
                final_page_dates = []
                for card in final_page_cards[-3:]:  # Check last 3 reviews
                    try:
                        card_text = card.text
                        for pattern in date_patterns:
                            matches = re.findall(pattern, card_text)
                            if matches:
                                for match in matches:
                                    try:
                                        if ',' in match:
                                            date_obj = datetime.strptime(match, '%b %d, %Y')
                                        elif '/' in match:
                                            date_obj = datetime.strptime(match, '%m/%d/%Y')
                                        elif '-' in match:
                                            date_obj = datetime.strptime(match, '%Y-%m-%d')
                                        else:
                                            continue

                                        final_page_dates.append(date_obj)
                                    except:
                                        continue
                    except:
                        continue

                oldest_date = min(final_page_dates) if final_page_dates else None
                print(f"📅 Oldest review: {oldest_date.strftime('%Y-%m-%d') if oldest_date else 'N/A'}")

        except Exception as e:
            print(f"Error in pagination navigation: {e}")

        # --- STEP 7: Calculate total reviews ---
        if total_pages > 1 and reviews_per_page > 0:
            calculated_total = (total_pages - 1) * reviews_per_page + last_page_reviews
        else:
            calculated_total = reviews_per_page

        print(f"🧮 Calculated total reviews: ({total_pages - 1} × {reviews_per_page}) + {last_page_reviews} = {calculated_total}")

        # Format results
        most_recent_str = most_recent_date.strftime('%Y-%m-%d') if most_recent_date else None
        oldest_str = oldest_date.strftime('%Y-%m-%d') if oldest_date else None

        print(f"✅ Smart analysis complete:")
        print(f"   Total reviews: {calculated_total}")
        print(f"   Most recent: {most_recent_str}")
        print(f"   Oldest: {oldest_str}")
        print(f"   Pages analyzed: {total_pages}")

        return {
            'total_reviews': calculated_total,
            'most_recent_review_date': most_recent_str,
            'oldest_review_date': oldest_str
        }

    except Exception as e:
        print(f"Error in smart review extraction: {e}")
        return {
            'total_reviews': 0,
            'most_recent_review_date': None,
            'oldest_review_date': None
        }

def scrape_etsy_products_with_reviews(search_query, max_products, min_delay, max_delay, csv_filename):
    """Main scraping function that combines product data with review analysis."""
    print("--- Starting Etsy Product & Review Analysis ---")
    print(f"Search Query: {search_query}")
    print(f"Max Products: {max_products}")
    print(f"Delay Between Products: {min_delay}-{max_delay} seconds")
    print(f"Output CSV: {csv_filename}")

    driver = None
    all_product_data = []

    try:
        driver = setup_etsy_chrome_driver()

        # Start with Etsy homepage to establish session
        print("🏠 Starting with Etsy homepage to establish session...")
        driver.get("https://www.etsy.com")
        time.sleep(random.uniform(5, 10))
        add_human_behavior(driver)

        # --- Step 1: Collect all product URLs (with duplicate prevention) ---
        product_urls = []
        seen_urls = set()  # Track URLs to prevent duplicates
        page = 1
        while len(product_urls) < max_products:
            encoded_query = search_query.replace(' ', '%20')
            search_url = f"https://www.etsy.com/search?q={encoded_query}&page={page}"
            print(f"\n--- Collecting URLs from Search Page {page} ---")

            # Clear cookies before search
            driver.delete_all_cookies()
            time.sleep(random.uniform(3, 5))

            driver.get(search_url)

            # Check for bot detection on search page
            if check_bot_detection(driver):
                print("🚨 Bot detection on search page! Stopping.")
                break

            time.sleep(random.uniform(8, 15))

            # Scroll to load products
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight/3);")
            time.sleep(3)
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(3)

            # Updated selector for current Etsy structure
            product_links = driver.find_elements(By.CSS_SELECTOR, 'a[href*="/listing/"]')
            if not product_links:
                print("No more products found on this page. Stopping URL collection.")
                break

            new_urls_found = 0
            for link in product_links:
                if len(product_urls) >= max_products:
                    break

                url = link.get_attribute('href')
                # Extract listing ID to prevent duplicates
                if '/listing/' in url:
                    listing_id = url.split('/listing/')[1].split('/')[0].split('?')[0]
                    if listing_id not in seen_urls:
                        seen_urls.add(listing_id)
                        product_urls.append(url)
                        new_urls_found += 1

            print(f"Found {len(product_links)} links, {new_urls_found} new unique URLs. Total URLs: {len(product_urls)}")

            if new_urls_found == 0:
                print("No new unique URLs found on this page. Stopping collection.")
                break

            page += 1

        print(f"\n--- Collected a total of {len(product_urls)} product URLs to analyze. ---")

        # --- Step 2: Process each product URL ---
        for i, url in enumerate(product_urls):
            print(f"\nProcessing product {i + 1}/{len(product_urls)}: {url}")
            try:
                # Clear cookies and navigate to neutral page first
                print("🧹 Clearing session and visiting neutral page...")
                driver.delete_all_cookies()
                driver.execute_script("window.localStorage.clear();")
                driver.execute_script("window.sessionStorage.clear();")

                # Visit Google first to reset session
                driver.get("https://www.google.com")
                time.sleep(random.uniform(5, 8))
                add_human_behavior(driver)

                # Then visit Etsy homepage
                driver.get("https://www.etsy.com")
                time.sleep(random.uniform(8, 12))
                add_human_behavior(driver)

                # Finally navigate to product page
                print(f"🎯 Navigating to product page...")
                driver.get(url)

                # Check for bot detection immediately
                if check_bot_detection(driver):
                    print("🚨 Bot detection triggered! Skipping this product.")
                    continue

                # Random delay with human-like patterns
                random_delay(min_delay, max_delay)

                # Add human behavior after page load
                add_human_behavior(driver)

                # Extract basic product info from the product page
                product_info = extract_product_info_from_html(driver.page_source, url, search_query)

                if product_info:
                    # Get review data using smart extraction method
                    review_data = extract_review_data_smart(driver)

                    # Combine data
                    combined_data = {**product_info, **review_data}
                    all_product_data.append(combined_data)

                    print(f"✅ Successfully analyzed product: {product_info['title'][:50]}...")
                    print(f"    Reviews Found: {review_data['total_reviews']}, Most Recent: {review_data['most_recent_review_date']}")
                else:
                    print("❌ Could not extract product info from page.")

            except Exception as e:
                print(f"❌ Critical error processing URL {url}: {e}")
                continue
        
        # --- Step 3: Save to CSV ---
        if all_product_data:
            fieldnames = [
                'search_query', 'title', 'price', 'product_url', 'image_url',
                'total_reviews', 'most_recent_review_date', 'oldest_review_date'
            ]
            
            with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(all_product_data)
            
            print(f"\n✅ Successfully saved {len(all_product_data)} products to {csv_filename}")
        
    except Exception as e:
        print(f"Critical error in main script: {e}")
        messagebox.showerror("Critical Error", f"An error occurred: {e}")
    
    finally:
        if driver:
            print("Closing browser...")
            driver.quit()
        
        print(f"\n--- Etsy Analysis Complete ---")
        print(f"Successfully analyzed {len(all_product_data)} products")
        print(f"Data saved to: {csv_filename}")

# --- Run the scraper ---
if __name__ == "__main__":
    # Check dependencies
    try:
        import undetected_chromedriver as uc
        from selenium.webdriver.common.by import By
        from bs4 import BeautifulSoup
    except ImportError as e:
        missing_lib = str(e).split("'")[-2]
        messagebox.showerror("Missing Libraries",
                             f"Required library '{missing_lib}' not found.\n"
                             f"Please install: pip install {missing_lib}")
        print(f"ERROR: Missing required library '{missing_lib}'.")
        exit()

    inputs = get_etsy_inputs_gui()
    if inputs:
        search_query, max_products, min_delay, max_delay, csv_filename = inputs
        scrape_etsy_products_with_reviews(search_query, max_products, min_delay, max_delay, csv_filename)
    else:
        print("\nScript cancelled by user.")
