#!/usr/bin/env python3
"""
Test script for the specific Etsy URL provided by user
Tests enhanced stealth and review detection
"""

import sys
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# Import our functions
from etsy import setup_etsy_chrome_driver, extract_review_data_smart, extract_product_info_from_html

def test_specific_etsy_url():
    """Test the specific URL provided by user"""
    
    print("🧪 Testing Specific Etsy URL")
    print("=" * 50)
    
    # The URL provided by user
    test_url = "https://www.etsy.com/listing/4344728865/tiffany-purchase-rings-womens-rings?click_key=71b999f4-25bb-43a8-88c9-a4f2fb9ace9f%3A3b0cfd6e27982486c93992c62e80198ec362d14d&click_sum=fbc063d0&ls=s&ga_order=most_relevant&ga_search_type=vintage&ga_view_type=gallery&ga_search_query=jewelry&ref=search_grid-708127-1-1&sr_prefetch=0&pro=1&pop=1&sts=1&content_source=71b999f4-25bb-43a8-88c9-a4f2fb9ace9f%253A3b0cfd6e27982486c93992c62e80198ec362d14d"
    
    driver = None
    
    try:
        print("🚀 Setting up enhanced stealth Chrome driver...")
        driver = setup_etsy_chrome_driver()
        
        print(f"🌐 Navigating to URL...")
        print(f"   URL: {test_url}")
        
        driver.get(test_url)
        time.sleep(8)  # Give extra time for page to load
        
        print("📄 Page loaded. Checking page content...")
        
        # Check if page loaded correctly
        try:
            page_title = driver.title
            print(f"   Page title: {page_title}")
            
            if "Etsy" not in page_title:
                print("⚠️ Warning: Page title doesn't contain 'Etsy'")
            
        except Exception as e:
            print(f"⚠️ Could not get page title: {e}")
        
        # Extract basic product info first
        print("\n🔍 Extracting product information...")
        product_info = extract_product_info_from_html(driver.page_source, test_url, "jewelry")
        
        if product_info:
            print("✅ Product info extracted:")
            for key, value in product_info.items():
                print(f"   {key}: {value}")
        else:
            print("❌ Could not extract product info")
        
        # Check for reviews section visibility
        print("\n🔍 Checking for review sections...")
        
        # Scroll to load content
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight/2);")
        time.sleep(2)
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(3)
        
        # Check for different review section indicators
        review_indicators = [
            ("Reviews for this item", "//h3[contains(text(), 'Reviews for this item')]"),
            ("Reviews for this shop", "//h3[contains(text(), 'Reviews for this shop')]"),
            ("This item text", "//div[contains(., 'This item')]"),
            ("Review cards", "//div[contains(@class, 'review')]"),
            ("Star ratings", "//div[contains(@class, 'star')]")
        ]
        
        found_indicators = []
        for name, xpath in review_indicators:
            try:
                elements = driver.find_elements(By.XPATH, xpath)
                if elements:
                    found_indicators.append(f"{name}: {len(elements)} found")
                    print(f"   ✅ {name}: {len(elements)} elements found")
                else:
                    print(f"   ❌ {name}: Not found")
            except Exception as e:
                print(f"   ❌ {name}: Error - {e}")
        
        # Take a screenshot for debugging
        try:
            screenshot_path = "debug_screenshot.png"
            driver.save_screenshot(screenshot_path)
            print(f"📸 Screenshot saved: {screenshot_path}")
        except Exception as e:
            print(f"⚠️ Could not save screenshot: {e}")
        
        # Now test our smart review extraction
        print("\n🧠 Testing Smart Review Extraction...")
        print("-" * 40)
        
        result = extract_review_data_smart(driver)
        
        print("\n📊 SMART EXTRACTION RESULTS:")
        print("=" * 35)
        print(f"Total Reviews: {result['total_reviews']}")
        print(f"Most Recent Review: {result['most_recent_review_date']}")
        print(f"Oldest Review: {result['oldest_review_date']}")
        
        # Validation
        print("\n✅ VALIDATION:")
        print("-" * 20)
        
        if result['total_reviews'] > 0:
            print("✅ Reviews found successfully")
        else:
            print("❌ No reviews found")
            print("🔍 Possible reasons:")
            print("   - Product has no reviews")
            print("   - Reviews are shop reviews, not item reviews")
            print("   - Anti-bot detection is blocking review content")
            print("   - Page structure has changed")
        
        if result['most_recent_review_date']:
            print("✅ Most recent date extracted")
        else:
            print("⚠️ No recent date found")
        
        if result['oldest_review_date']:
            print("✅ Oldest date extracted")
        else:
            print("⚠️ No oldest date found")
        
        # Additional debugging info
        print(f"\n🔧 DEBUG INFO:")
        print(f"   Found review indicators: {len(found_indicators)}")
        for indicator in found_indicators:
            print(f"   - {indicator}")
        
        print(f"\n🎉 Test completed!")
        
        return result
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return None
        
    finally:
        if driver:
            print("\n🔄 Keeping browser open for 10 seconds for manual inspection...")
            time.sleep(10)
            print("Closing browser...")
            driver.quit()

def debug_page_content():
    """Debug function to examine page content in detail"""
    
    print("🔍 Debug Mode: Examining Page Content")
    print("=" * 40)
    
    test_url = "https://www.etsy.com/listing/4344728865/tiffany-purchase-rings-womens-rings?click_key=71b999f4-25bb-43a8-88c9-a4f2fb9ace9f%3A3b0cfd6e27982486c93992c62e80198ec362d14d&click_sum=fbc063d0&ls=s&ga_order=most_relevant&ga_search_type=vintage&ga_view_type=gallery&ga_search_query=jewelry&ref=search_grid-708127-1-1&sr_prefetch=0&pro=1&pop=1&sts=1&content_source=71b999f4-25bb-43a8-88c9-a4f2fb9ace9f%253A3b0cfd6e27982486c93992c62e80198ec362d14d"
    
    driver = setup_etsy_chrome_driver()
    
    try:
        driver.get(test_url)
        time.sleep(8)
        
        # Scroll to load content
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(5)
        
        # Get page source and search for review-related content
        page_source = driver.page_source
        
        # Search for key phrases
        key_phrases = [
            "Reviews for this item",
            "Reviews for this shop", 
            "This item",
            "review",
            "star",
            "rating"
        ]
        
        print("🔍 Searching page source for key phrases:")
        for phrase in key_phrases:
            count = page_source.lower().count(phrase.lower())
            print(f"   '{phrase}': {count} occurrences")
        
        # Save page source for manual inspection
        with open("debug_page_source.html", "w", encoding="utf-8") as f:
            f.write(page_source)
        print("💾 Page source saved to: debug_page_source.html")
        
        print("\n⏸️ Browser will stay open for manual inspection...")
        input("Press Enter to close browser...")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    print("Specific URL Test for Etsy Scraper")
    print("=" * 40)
    print("1. Run smart review extraction test")
    print("2. Debug page content")
    
    choice = input("\nChoose test type (1 or 2): ").strip()
    
    if choice == "1":
        test_specific_etsy_url()
    elif choice == "2":
        debug_page_content()
    else:
        print("Invalid choice")
